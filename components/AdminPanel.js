function AdminPanel({ user, onBack }) {
  try {
    const [activeTab, setActiveTab] = React.useState('books');

    // 检查用户权限
    React.useEffect(() => {
      console.log('AdminPanel - User:', user);
      console.log('AdminPanel - User role:', user?.role);
    }, [user]);

    if (!user) {
      return (
        <div className="max-w-4xl mx-auto text-center py-12" data-name="admin-panel-unauthorized" data-file="components/AdminPanel.js">
          <div className="icon-shield-x text-4xl text-red-500 mb-4"></div>
          <h2 className="text-2xl font-bold text-[var(--text-primary)] mb-2">访问被拒绝</h2>
          <p className="text-[var(--text-secondary)]">请先登录</p>
        </div>
      );
    }

    if (user.role !== 'admin') {
      return (
        <div className="max-w-4xl mx-auto text-center py-12" data-name="admin-panel-unauthorized" data-file="components/AdminPanel.js">
          <div className="icon-shield-x text-4xl text-red-500 mb-4"></div>
          <h2 className="text-2xl font-bold text-[var(--text-primary)] mb-2">访问被拒绝</h2>
          <p className="text-[var(--text-secondary)]">您没有权限访问管理功能</p>
          <p className="text-xs text-gray-400 mt-2">当前角色: {user.role}</p>
        </div>
      );
    }

    return (
      <div className="max-w-6xl mx-auto" data-name="admin-panel" data-file="components/AdminPanel.js">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
        >
          <div className="icon-arrow-left text-xl"></div>
          <span>返回首页</span>
        </button>

        <div className="mb-6">
          <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-2">管理中心</h1>
          <p className="text-[var(--text-secondary)]">管理图书和评论内容</p>
        </div>

        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="border-b border-[var(--border-color)]">
            <nav className="flex">
              <button
                onClick={() => setActiveTab('books')}
                className={`px-6 py-4 text-sm font-medium ${
                  activeTab === 'books'
                    ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                    : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <div className="icon-book text-lg"></div>
                  <span>图书管理</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('reviews')}
                className={`px-6 py-4 text-sm font-medium ${
                  activeTab === 'reviews'
                    ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                    : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <div className="icon-message-circle text-lg"></div>
                  <span>评论管理</span>
                </div>
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'books' ? (
              <BookManagement />
            ) : (
              <ReviewManagement />
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('AdminPanel component error:', error);
    return null;
  }
}