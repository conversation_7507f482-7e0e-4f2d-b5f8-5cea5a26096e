function ReviewManagement() {
  try {
    const [reviews, setReviews] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    // 从父组件获取用户信息进行权限验证
    const user = React.useContext ? null : AuthService.getUser();
    
    React.useEffect(() => {
      if (user && user.role !== 'admin') {
        console.warn('Non-admin user attempting to access review management');
      }
    }, [user]);

    const loadReviews = React.useCallback(async () => {
      try {
        setLoading(true);
        const reviewsData = await ApiService.getAllReviews();
        setReviews(reviewsData || []);
      } catch (error) {
        console.error('加载评论失败:', error);
        setReviews([]);
      } finally {
        setLoading(false);
      }
    }, []);

    React.useEffect(() => {
      loadReviews();
    }, [loadReviews]);

    const handleDeleteReview = async (reviewId) => {
      if (!confirm('确定要删除这条评论吗？')) return;
      
      try {
        await ApiService.deleteReview(reviewId);
        setReviews(reviews.filter(review => review.id !== reviewId));
      } catch (error) {
        alert('删除失败，请重试');
      }
    };

    const renderStars = (rating) => {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        stars.push(
          <div 
            key={i} 
            className={`icon-star text-sm ${i <= rating ? 'rating-stars' : 'text-gray-300'}`}
          />
        );
      }
      return stars;
    };

    if (loading) {
      return (
        <div className="text-center py-8">
          <div className="w-8 h-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-[var(--text-muted)]">加载中...</p>
        </div>
      );
    }

    return (
      <div data-name="review-management" data-file="components/ReviewManagement.js">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-[var(--text-primary)]">评论管理</h2>
          <p className="text-sm text-[var(--text-secondary)]">共 {reviews.length} 条评论</p>
        </div>

        {reviews.length === 0 ? (
          <div className="text-center py-8">
            <div className="icon-message-circle text-4xl text-gray-300 mb-4"></div>
            <p className="text-[var(--text-muted)]">暂无评论</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map(review => (
              <div key={review.id} className="p-4 border border-[var(--border-color)] rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-[var(--text-primary)]">
                      用户ID: {review.userId}
                    </span>
                    <div className="flex items-center space-x-1">
                      {renderStars(review.rating)}
                    </div>
                  </div>
                  <button
                    onClick={() => handleDeleteReview(review.id)}
                    className="px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded"
                  >
                    删除
                  </button>
                </div>
                
                <p className="text-[var(--text-secondary)] mb-2 leading-relaxed">
                  {review.content}
                </p>
                
                <div className="flex items-center justify-between text-xs text-[var(--text-muted)]">
                  <span>图书ID: {review.bookId}</span>
                  <span>{new Date(review.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('ReviewManagement component error:', error);
    return null;
  }
}