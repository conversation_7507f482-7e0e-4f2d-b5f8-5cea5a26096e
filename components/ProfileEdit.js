function ProfileEdit({ user, onClose, onProfileUpdated }) {
  try {
    const [formData, setFormData] = React.useState({
      username: user.username || '',
      avatar: user.avatar || '',
      bio: user.bio || ''
    });
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState('');

    const handleSubmit = async (e) => {
      e.preventDefault();
      if (!formData.username.trim()) {
        setError('用户名不能为空');
        return;
      }

      try {
        setLoading(true);
        setError('');
        
        const updatedUser = await ApiService.updateUser(user.id, {
          ...user,
          username: formData.username.trim(),
          avatar: formData.avatar || user.avatar,
          bio: formData.bio.trim()
        });
        
        // 更新本地存储
        AuthService.setUser(updatedUser);
        onProfileUpdated(updatedUser);
      } catch (err) {
        setError('更新失败，请重试');
      } finally {
        setLoading(false);
      }
    };

    const handleChange = (field, value) => {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" data-name="profile-edit" data-file="components/ProfileEdit.js">
        <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-[var(--text-primary)]">编辑资料</h2>
            <button
              onClick={onClose}
              className="text-[var(--text-muted)] hover:text-[var(--text-primary)]"
            >
              <div className="icon-x text-xl"></div>
            </button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                用户名
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                placeholder="请输入用户名"
                required
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                头像链接
              </label>
              <input
                type="url"
                value={formData.avatar}
                onChange={(e) => handleChange('avatar', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                placeholder="请输入头像图片链接"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                个人简介
              </label>
              <textarea
                value={formData.bio}
                onChange={(e) => handleChange('bio', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                rows="3"
                placeholder="介绍一下你自己..."
              />
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '保存中...' : '保存'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  } catch (error) {
    console.error('ProfileEdit component error:', error);
    return null;
  }
}