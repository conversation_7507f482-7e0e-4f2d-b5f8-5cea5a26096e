function ReviewList({ reviews, loading, user, bookId, onReviewSubmitted }) {
  try {
    const [showReviewForm, setShowReviewForm] = React.useState(false);
    const [newReview, setNewReview] = React.useState({ rating: 5, content: '' });
    const [submitting, setSubmitting] = React.useState(false);

    const handleSubmitReview = async (e) => {
      e.preventDefault();
      if (!user) {
        alert('请先登录后再评价');
        return;
      }

      try {
        setSubmitting(true);
        await ApiService.createReview({
          bookId,
          userId: user.id,
          rating: newReview.rating,
          content: newReview.content
        });
        setNewReview({ rating: 5, content: '' });
        setShowReviewForm(false);
        
        // 通知父组件刷新图书统计
        if (onReviewSubmitted) {
          onReviewSubmitted();
        }
      } catch (error) {
        alert('评论提交失败，请重试');
      } finally {
        setSubmitting(false);
      }
    };

    const renderStars = (rating) => {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        stars.push(
          <div 
            key={i} 
            className={`icon-star text-lg ${i <= rating ? 'rating-stars' : 'text-gray-300'}`}
          />
        );
      }
      return stars;
    };

    return (
      <div className="bg-white rounded-lg shadow-lg p-6" data-name="review-list" data-file="components/ReviewList.js">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-[var(--text-primary)]">
            用户评价 ({reviews.length})
          </h2>
          {user && (
            <button
              onClick={() => setShowReviewForm(!showReviewForm)}
              className="btn-primary"
            >
              写评价
            </button>
          )}
        </div>

        {showReviewForm && (
          <form onSubmit={handleSubmitReview} className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                评分
              </label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map(star => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setNewReview({...newReview, rating: star})}
                    className={`icon-star text-2xl ${
                      star <= newReview.rating ? 'rating-stars' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                评价内容
              </label>
              <textarea
                value={newReview.content}
                onChange={(e) => setNewReview({...newReview, content: e.target.value})}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                rows="4"
                placeholder="分享你的阅读感受..."
                required
              />
            </div>
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={submitting}
                className="btn-primary disabled:opacity-50"
              >
                {submitting ? '提交中...' : '提交评价'}
              </button>
              <button
                type="button"
                onClick={() => setShowReviewForm(false)}
                className="px-4 py-2 text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
              >
                取消
              </button>
            </div>
          </form>
        )}

        {loading ? (
          <div className="text-center py-8">
            <div className="w-8 h-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-[var(--text-muted)]">加载评价中...</p>
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-8">
            <div className="icon-message-circle text-4xl text-gray-300 mb-4"></div>
            <p className="text-[var(--text-muted)]">还没有评价，来写第一个吧！</p>
          </div>
        ) : (
          <div className="space-y-6">
            {reviews.map(review => (
              <div key={review.id} className="border-b border-[var(--border-color)] pb-6 last:border-b-0">
                <div className="flex items-start space-x-4">
                  <img
                    src={`https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face`}
                    alt="用户头像"
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium text-[var(--text-primary)]">匿名用户</span>
                      <div className="flex items-center space-x-1">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                    <p className="text-[var(--text-secondary)] leading-relaxed mb-2">
                      {review.content}
                    </p>
                    <p className="text-xs text-[var(--text-muted)]">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('ReviewList component error:', error);
    return null;
  }
}