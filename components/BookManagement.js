function BookManagement() {
  try {
    const [books, setBooks] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showAddForm, setShowAddForm] = React.useState(false);
    const [editingBook, setEditingBook] = React.useState(null);

    // 从父组件获取用户信息进行权限验证
    const user = React.useContext ? null : AuthService.getUser();
    
    React.useEffect(() => {
      if (user && user.role !== 'admin') {
        console.warn('Non-admin user attempting to access book management');
      }
    }, [user]);

    const loadBooks = React.useCallback(async () => {
      try {
        setLoading(true);
        const booksData = await ApiService.getBooks();
        setBooks(booksData || []);
      } catch (error) {
        console.error('加载图书失败:', error);
        setBooks([]);
      } finally {
        setLoading(false);
      }
    }, []);

    React.useEffect(() => {
      loadBooks();
    }, [loadBooks]);

    const handleDeleteBook = async (bookId) => {
      if (!confirm('确定要删除这本图书吗？')) return;
      
      try {
        await ApiService.deleteBook(bookId);
        setBooks(books.filter(book => book.id !== bookId));
      } catch (error) {
        alert('删除失败，请重试');
      }
    };

    const handleBookSaved = () => {
      setShowAddForm(false);
      setEditingBook(null);
      loadBooks();
    };

    if (loading) {
      return (
        <div className="text-center py-8">
          <div className="w-8 h-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-[var(--text-muted)]">加载中...</p>
        </div>
      );
    }

    return (
      <div data-name="book-management" data-file="components/BookManagement.js">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-[var(--text-primary)]">图书管理</h2>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary"
          >
            添加图书
          </button>
        </div>

        <div className="space-y-4">
          {books.map(book => (
            <div key={book.id} className="flex items-center space-x-4 p-4 border border-[var(--border-color)] rounded-lg">
              <img
                src={book.cover}
                alt={book.title}
                className="w-12 h-16 object-cover rounded"
              />
              <div className="flex-1">
                <h3 className="font-medium text-[var(--text-primary)]">{book.title}</h3>
                <p className="text-sm text-[var(--text-secondary)]">{book.author}</p>
                <p className="text-xs text-[var(--text-muted)]">¥{book.price}</p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setEditingBook(book)}
                  className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
                >
                  编辑
                </button>
                <button
                  onClick={() => handleDeleteBook(book.id)}
                  className="px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded"
                >
                  删除
                </button>
              </div>
            </div>
          ))}
        </div>

        {(showAddForm || editingBook) && (
          <BookForm
            book={editingBook}
            onClose={() => {
              setShowAddForm(false);
              setEditingBook(null);
            }}
            onSave={handleBookSaved}
          />
        )}
      </div>
    );
  } catch (error) {
    console.error('BookManagement component error:', error);
    return null;
  }
}

function BookForm({ book, onClose, onSave }) {
  const [formData, setFormData] = React.useState({
    title: book?.title || '',
    author: book?.author || '',
    cover: book?.cover || '',
    description: book?.description || '',
    publisher: book?.publisher || '',
    publishYear: book?.publishYear || '',
    price: book?.price || '',
    category: book?.category || 'literature',
    rating: book?.rating || 4.0,
    reviewCount: book?.reviewCount || 0
  });
  const [loading, setLoading] = React.useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (book) {
        await ApiService.updateBook(book.id, formData);
      } else {
        await ApiService.createBook(formData);
      }
      onSave();
    } catch (error) {
      alert('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-bold mb-4">{book ? '编辑图书' : '添加图书'}</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="text"
            placeholder="书名"
            value={formData.title}
            onChange={(e) => setFormData({...formData, title: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg"
            required
          />
          <input
            type="text"
            placeholder="作者"
            value={formData.author}
            onChange={(e) => setFormData({...formData, author: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg"
            required
          />
          <input
            type="url"
            placeholder="封面链接"
            value={formData.cover}
            onChange={(e) => setFormData({...formData, cover: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg"
            required
          />
          <textarea
            placeholder="图书描述"
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg"
            rows="3"
            required
          />
          <div className="grid grid-cols-2 gap-2">
            <input
              type="text"
              placeholder="出版社"
              value={formData.publisher}
              onChange={(e) => setFormData({...formData, publisher: e.target.value})}
              className="px-3 py-2 border rounded-lg"
              required
            />
            <input
              type="text"
              placeholder="出版年份"
              value={formData.publishYear}
              onChange={(e) => setFormData({...formData, publishYear: e.target.value})}
              className="px-3 py-2 border rounded-lg"
              required
            />
          </div>
          <input
            type="text"
            placeholder="价格"
            value={formData.price}
            onChange={(e) => setFormData({...formData, price: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg"
            required
          />
          
          <div className="flex space-x-2">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 btn-primary disabled:opacity-50"
            >
              {loading ? '保存中...' : '保存'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-[var(--text-secondary)]"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}