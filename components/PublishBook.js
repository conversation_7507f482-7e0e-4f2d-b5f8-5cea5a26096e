function PublishBook({ user, onBack, onBookPublished }) {
  try {
    const [formData, setFormData] = React.useState({
      title: '',
      author: '',
      cover: '',
      description: '',
      publisher: '',
      publishYear: '',
      price: '',
      isbn: '',
      category: 'literature'
    });
    const [loading, setLoading] = React.useState(false);
    const [isbnLoading, setIsbnLoading] = React.useState(false);
    const [error, setError] = React.useState('');
    const [isbnVerified, setIsbnVerified] = React.useState(false);
    const [isbnData, setIsbnData] = React.useState(null);

    const categories = [
      { id: 'fiction', name: '小说' },
      { id: 'literature', name: '文学' },
      { id: 'history', name: '历史' },
      { id: 'philosophy', name: '哲学' },
      { id: 'psychology', name: '心理学' },
      { id: 'science', name: '科学' },
      { id: 'biography', name: '传记' }
    ];

    const queryISBN = async () => {
      if (!formData.isbn.trim()) {
        setError('请输入ISBN号');
        return;
      }

      // 由于浏览器CORS限制，暂时无法直接调用第三方API
      // 提示用户手动输入图书信息
      setError('由于浏览器安全限制，暂时无法自动查询ISBN信息。请点击"手动输入"按钮手动填写图书信息。');
      setIsbnVerified(false);
      setIsbnData(null);
    };

    const enableManualInput = () => {
      setIsbnVerified(true);
      setError('');
      // 清空自动填充的字段，允许手动输入
      setFormData(prev => ({
        ...prev,
        title: '',
        author: '',
        cover: '',
        description: '',
        publisher: '',
        publishYear: '',
        price: ''
      }));
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      if (!user || user.role !== 'common') {
        setError('只有普通用户可以发布图书');
        return;
      }

      if (!isbnVerified) {
        setError('请先查询并验证ISBN号，确保图书信息正确');
        return;
      }

      try {
        setLoading(true);
        setError('');
        
        await ApiService.createBook({
          ...formData,
          publishedBy: user.id,
          status: 'published'
        });
        
        alert('图书发布成功！');
        onBookPublished();
      } catch (err) {
        setError('发布失败，请重试');
      } finally {
        setLoading(false);
      }
    };

    const handleChange = (field, value) => {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
      
      // 如果修改了ISBN，重置验证状态
      if (field === 'isbn') {
        setIsbnVerified(false);
        setIsbnData(null);
        setError('');
      }
    };

    return (
      <div className="max-w-2xl mx-auto" data-name="publish-book" data-file="components/PublishBook.js">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
        >
          <div className="icon-arrow-left text-xl"></div>
          <span>返回首页</span>
        </button>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-[var(--text-primary)] mb-6">发布图书</h1>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                ISBN号 *
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={formData.isbn}
                  onChange={(e) => handleChange('isbn', e.target.value)}
                  className="flex-1 px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                  placeholder="请输入ISBN号，如：9787559855022"
                  required
                />
                <button
                  type="button"
                  onClick={enableManualInput}
                  className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-lg hover:bg-[var(--primary-hover)]"
                >
                  手动输入
                </button>
              </div>
              {isbnVerified && (
                <p className="text-sm text-green-600 mt-1">
                  ✓ 已启用手动输入模式，请手动填写图书信息
                </p>
              )}
              <p className="text-xs text-[var(--text-muted)] mt-1">
                提示：由于浏览器安全限制，需要手动填写图书信息
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                书名 *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                placeholder="请输入书名"
                required
                disabled={!isbnVerified}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                作者 *
              </label>
              <input
                type="text"
                value={formData.author}
                onChange={(e) => handleChange('author', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                placeholder="请输入作者姓名"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                封面链接 *
              </label>
              <input
                type="url"
                value={formData.cover}
                onChange={(e) => handleChange('cover', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                placeholder="请输入封面图片链接"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                分类 *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                required
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  出版社 *
                </label>
                <input
                  type="text"
                  value={formData.publisher}
                  onChange={(e) => handleChange('publisher', e.target.value)}
                  className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                  placeholder="出版社"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  出版年份 *
                </label>
                <input
                  type="text"
                  value={formData.publishYear}
                  onChange={(e) => handleChange('publishYear', e.target.value)}
                  className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                  placeholder="2024"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  ISBN号 *
                </label>
                <input
                  type="text"
                  value={formData.isbn}
                  onChange={(e) => handleChange('isbn', e.target.value)}
                  className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                  placeholder="978-7-XXXXXXX-X"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  价格 *
                </label>
                <input
                  type="text"
                  value={formData.price}
                  onChange={(e) => handleChange('price', e.target.value)}
                  className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                  placeholder="29.80"
                  required
                />
              </div>
            </div>



            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                图书描述 *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)]"
                rows="4"
                placeholder="请输入图书简介..."
                required
              />
            </div>

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <div className="flex space-x-4 pt-4">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '发布中...' : '发布图书'}
              </button>
              <button
                type="button"
                onClick={onBack}
                className="px-6 py-2 text-[var(--text-secondary)] hover:text-[var(--text-primary)] border border-[var(--border-color)] rounded-lg"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  } catch (error) {
    console.error('PublishBook component error:', error);
    return null;
  }
}