function BookDetail({ book, user, onBack, onBookStatsUpdate }) {
  try {
    const [reviews, setReviews] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [bookStats, setBookStats] = React.useState({
      rating: book.rating,
      reviewCount: book.reviewCount
    });

    const loadReviews = React.useCallback(async () => {
      try {
        setLoading(true);
        const reviewsData = await ApiService.getReviewsByBookId(book.id);
        setReviews(reviewsData);
        
        // 同时更新评论统计
        const stats = await ApiService.getBookReviewStats(book.id);
        setBookStats(stats);
        
        // 通知父组件更新图书统计
        if (onBookStatsUpdate) {
          onBookStatsUpdate(book.id, stats);
        }
      } catch (error) {
        console.error('加载评论失败:', error);
      } finally {
        setLoading(false);
      }
    }, [book.id, onBookStatsUpdate]);

    React.useEffect(() => {
      loadReviews();
    }, [loadReviews]);

    const handleReviewSubmitted = () => {
      loadReviews();
    };

    const renderStars = (rating) => {
      const stars = [];
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating % 1 !== 0;
      
      for (let i = 0; i < fullStars; i++) {
        stars.push(<div key={i} className="icon-star text-lg rating-stars"></div>);
      }
      
      if (hasHalfStar) {
        stars.push(<div key="half" className="icon-star-half text-lg rating-stars"></div>);
      }
      
      const emptyStars = 5 - Math.ceil(rating);
      for (let i = 0; i < emptyStars; i++) {
        stars.push(<div key={`empty-${i}`} className="icon-star text-lg text-gray-300"></div>);
      }
      
      return stars;
    };

    return (
      <div className="max-w-4xl mx-auto" data-name="book-detail" data-file="components/BookDetail.js">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
        >
          <div className="icon-arrow-left text-xl"></div>
          <span>返回图书列表</span>
        </button>

        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-8">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="flex-shrink-0">
                <img
                  src={book.cover}
                  alt={book.title}
                  className="w-48 h-64 object-cover rounded-lg shadow-md"
                />
              </div>
              
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-2">
                  {book.title}
                </h1>
                <p className="text-xl text-[var(--text-secondary)] mb-4">{book.author}</p>
                
                <div className="flex items-center space-x-3 mb-4">
                  <div className="flex items-center space-x-1">
                    {renderStars(book.rating)}
                  </div>
                  <span className="text-lg font-medium text-[var(--text-primary)]">
                    {bookStats.averageRating || book.rating}
                  </span>
                  <span className="text-[var(--text-muted)]">
                    ({bookStats.reviewCount}人评价)
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
                  <div>
                    <span className="text-[var(--text-muted)]">出版社：</span>
                    <span className="text-[var(--text-primary)]">{book.publisher}</span>
                  </div>
                  <div>
                    <span className="text-[var(--text-muted)]">出版年：</span>
                    <span className="text-[var(--text-primary)]">{book.publishYear}</span>
                  </div>
                  {book.isbn && (
                    <div>
                      <span className="text-[var(--text-muted)]">ISBN：</span>
                      <span className="text-[var(--text-primary)]">{book.isbn}</span>
                    </div>
                  )}
                  <div>
                    <span className="text-[var(--text-muted)]">价格：</span>
                    <span className="text-xl font-medium text-[var(--primary-color)]">
                      ¥{book.price}
                    </span>
                  </div>
                </div>
                
                <p className="text-[var(--text-secondary)] leading-relaxed">
                  {book.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <ReviewList 
            reviews={reviews} 
            loading={loading}
            user={user}
            bookId={book.id}
            onReviewSubmitted={handleReviewSubmitted}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('BookDetail component error:', error);
    return null;
  }
}
