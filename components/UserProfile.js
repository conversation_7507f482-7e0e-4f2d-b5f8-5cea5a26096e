function UserProfile({ user, onBack, onProfileUpdate }) {
  try {
    const [userReviews, setUserReviews] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showEditProfile, setShowEditProfile] = React.useState(false);

    React.useEffect(() => {
      const loadUserReviews = async () => {
        try {
          const reviews = await ApiService.getUserReviews(user.id);
          setUserReviews(reviews);
        } catch (error) {
          console.error('加载用户评论失败:', error);
        } finally {
          setLoading(false);
        }
      };

      loadUserReviews();
    }, [user.id]);

    const renderStars = (rating) => {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        stars.push(
          <div 
            key={i} 
            className={`icon-star text-sm ${i <= rating ? 'rating-stars' : 'text-gray-300'}`}
          />
        );
      }
      return stars;
    };

    const handleProfileUpdated = (updatedUser) => {
      setShowEditProfile(false);
      onProfileUpdate(updatedUser);
    };

    return (
      <div className="max-w-4xl mx-auto" data-name="user-profile" data-file="components/UserProfile.js">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
        >
          <div className="icon-arrow-left text-xl"></div>
          <span>返回首页</span>
        </button>

        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
          <div className="p-8">
            <div className="flex flex-col md:flex-row items-start gap-6">
              <img
                src={user.avatar}
                alt={user.username}
                className="w-24 h-24 rounded-full object-cover"
              />
              
              <div className="flex-1">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                    {user.username}
                  </h1>
                  <button
                    onClick={() => setShowEditProfile(true)}
                    className="btn-primary text-sm"
                  >
                    编辑资料
                  </button>
                </div>
                
                <p className="text-[var(--text-secondary)] mb-4">
                  {user.bio || '这个人很懒，什么都没有留下...'}
                </p>
                
                <div className="flex items-center space-x-6 text-sm text-[var(--text-muted)]">
                  <div>
                    <span className="font-medium">{userReviews.length}</span>
                    <span className="ml-1">条评价</span>
                  </div>
                  <div>
                    <span className="font-medium">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </span>
                    <span className="ml-1">加入</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-[var(--text-primary)] mb-6">
            我的评价 ({userReviews.length})
          </h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <p className="text-[var(--text-muted)]">加载中...</p>
            </div>
          ) : userReviews.length === 0 ? (
            <div className="text-center py-8">
              <div className="icon-message-circle text-4xl text-gray-300 mb-4"></div>
              <p className="text-[var(--text-muted)]">还没有评价，去看看书吧！</p>
            </div>
          ) : (
            <div className="space-y-4">
              {userReviews.map(review => (
                <div key={review.id} className="border border-[var(--border-color)] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-[var(--text-primary)]">图书评价</span>
                      <div className="flex items-center space-x-1">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                    <span className="text-xs text-[var(--text-muted)]">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-[var(--text-secondary)] leading-relaxed">
                    {review.content}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>

        {showEditProfile && (
          <ProfileEdit
            user={user}
            onClose={() => setShowEditProfile(false)}
            onProfileUpdated={handleProfileUpdated}
          />
        )}
      </div>
    );
  } catch (error) {
    console.error('UserProfile component error:', error);
    return null;
  }
}