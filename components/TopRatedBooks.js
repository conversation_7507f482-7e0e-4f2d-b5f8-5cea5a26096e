function TopRatedBooks({ books, onBack, onBookSelect }) {
  try {
    // 按评分倒序排列图书
    const topRatedBooks = React.useMemo(() => {
      return [...books]
        .sort((a, b) => {
          // 先按评分排序，评分相同则按评价数量排序
          if (b.rating !== a.rating) {
            return b.rating - a.rating;
          }
          return b.reviewCount - a.reviewCount;
        })
        .slice(0, 20); // 显示前20本图书
    }, [books]);

    const renderStars = (rating) => {
      const stars = [];
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating % 1 !== 0;
      
      for (let i = 0; i < fullStars; i++) {
        stars.push(<div key={i} className="icon-star text-sm rating-stars"></div>);
      }
      
      if (hasHalfStar) {
        stars.push(<div key="half" className="icon-star-half text-sm rating-stars"></div>);
      }
      
      const emptyStars = 5 - Math.ceil(rating);
      for (let i = 0; i < emptyStars; i++) {
        stars.push(<div key={`empty-${i}`} className="icon-star text-sm text-gray-300"></div>);
      }
      
      return stars;
    };

    return (
      <div className="max-w-4xl mx-auto" data-name="top-rated-books" data-file="components/TopRatedBooks.js">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
        >
          <div className="icon-arrow-left text-xl"></div>
          <span>返回首页</span>
        </button>

        <div className="mb-6">
          <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-2">好评榜单</h1>
          <p className="text-[var(--text-secondary)]">根据用户评分排序的优质图书推荐</p>
        </div>

        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <div className="space-y-4">
              {topRatedBooks.map((book, index) => (
                <div 
                  key={book.id}
                  className="flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                  onClick={() => onBookSelect(book)}
                >
                  <div className="flex-shrink-0 w-8 text-center">
                    <span className={`text-lg font-bold ${
                      index < 3 ? 'text-[var(--rating-color)]' : 'text-[var(--text-muted)]'
                    }`}>
                      {index + 1}
                    </span>
                  </div>
                  
                  <img
                    src={book.cover}
                    alt={book.title}
                    className="w-16 h-20 object-cover rounded shadow-sm flex-shrink-0"
                  />
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-[var(--text-primary)] mb-1 line-clamp-1">
                      {book.title}
                    </h3>
                    <p className="text-[var(--text-secondary)] mb-2">{book.author}</p>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="flex items-center space-x-1">
                        {renderStars(book.rating)}
                      </div>
                      <span className="text-sm font-medium text-[var(--text-primary)]">
                        {book.rating}
                      </span>
                      <span className="text-sm text-[var(--text-muted)]">
                        ({book.reviewCount}人评价)
                      </span>
                    </div>
                    
                    <p className="text-sm text-[var(--text-secondary)] line-clamp-1">
                      {book.description}
                    </p>
                  </div>
                  
                  <div className="flex-shrink-0 text-right">
                    <span className="text-lg font-medium text-[var(--primary-color)]">
                      ¥{book.price}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('TopRatedBooks component error:', error);
    return null;
  }
}