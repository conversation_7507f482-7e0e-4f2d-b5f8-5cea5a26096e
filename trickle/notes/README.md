# 花瓣读书网站

## 项目简介

这是一个仿豆瓣读书的网站，提供图书浏览、搜索、分类筛选等功能。

## 主要功能
- 📚 图书展示：以卡片形式展示图书信息
- 🔍 搜索功能：支持按书名、作者搜索
- 🏷️ 分类筛选：按文学、小说、历史等分类浏览
- ⭐ 评分展示：显示图书评分和评价数量
- 📱 响应式设计：适配不同设备尺寸

## 技术栈
- React 18
- TailwindCSS
- Lucide Icons
- CORS代理服务（解决第三方API跨域问题）

## 文件结构
```
├── index.html              # 主页面
├── app.js                  # 主应用组件
├── components/             # UI组件
│   ├── Header.js          # 头部组件
│   ├── SearchBar.js       # 搜索栏
│   ├── CategoryNav.js     # 分类导航
│   ├── BookCard.js        # 图书卡片
│   └── BookGrid.js        # 图书网格
├── utils/                 # 工具函数
│   └── bookData.js        # 图书数据
└── trickle/               # 项目资源
    ├── assets/            # 图片资源
    └── notes/             # 项目文档
```

## 设计特色
- 简洁清晰的卡片式布局
- 直观的星级评分显示

- 流畅的交互体验

## 使用说明
1. 打开 index.html 即可访问网站
2. 使用搜索栏搜索感兴趣的图书
3. 点击分类标签筛选不同类型的图书
4. 浏览图书详细信息和评分
5. 普通用户可以发布新图书（支持ISBN自动查询）
6. 管理员可以管理图书和评论

## 技术说明
- ISBN输入功能采用手动输入模式，确保功能稳定性
- 由于浏览器CORS限制，暂时无法直接调用第三方ISBN查询API
- 用户需要手动填写图书的详细信息
