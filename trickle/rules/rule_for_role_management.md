When managing user roles and access control
- Only rely on the 'role' field in the user database table to determine user permissions
- Do not use email patterns or other criteria to automatically assign admin roles
- New users are created with 'common' role by default
- Admin role must be manually set in the database by updating the user's role field to 'admin'
- Admin menu and functions are only visible and accessible when user.role === 'admin'
- Always verify user role from the database, not from client-side assumptions