import React, { useState } from 'react'
import { useAuth } from './hooks/useAuth'
import { useBooks } from './hooks/useBooks'
import Header from './components/Header'
import SearchBar from './components/SearchBar'
import CategoryNav from './components/CategoryNav'
import BookGrid from './components/BookGrid'
import LoadingSpinner from './components/LoadingSpinner'
import LoginModal from './components/LoginModal'
import BookDetail from './components/BookDetail'
import UserProfile from './components/UserProfile'
import TopRatedBooks from './components/TopRatedBooks'
import AdminPanel from './components/AdminPanel'
import PublishBook from './components/PublishBook'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo.componentStack);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
            <p className="text-gray-600 mb-4">We're sorry, but something unexpected happened.</p>
            <button
              onClick={() => window.location.reload()}
              className="btn btn-black"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function App() {
  try {
    const { user, login, logout, loading: authLoading, setUser } = useAuth();
    const { 
      books, 
      filteredBooks, 
      loading: booksLoading, 
      searchBooks, 
      filterByCategory 
    } = useBooks();
    
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [showLoginModal, setShowLoginModal] = useState(false);
    const [selectedBook, setSelectedBook] = useState(null);
    const [showProfile, setShowProfile] = useState(false);
    const [showTopRated, setShowTopRated] = useState(false);
    const [showAdminPanel, setShowAdminPanel] = useState(false);
    const [showPublishBook, setShowPublishBook] = useState(false);

    const handleSearch = (term) => {
      searchBooks(term);
    };

    const handleCategoryChange = (category) => {
      setSelectedCategory(category);
      filterByCategory(category);
    };

    const handleBookSelect = (book) => {
      setSelectedBook(book);
    };

    const handleBookStatsUpdate = (bookId, newStats) => {
      // 这里可以更新图书列表中的统计信息，但为了简化暂时不实现
      // 实际项目中应该更新books状态中对应图书的统计数据
    };

    const handleLoginClick = () => {
      setShowLoginModal(true);
    };

    const handleLoginSuccess = (userData) => {
      login(userData);
      setShowLoginModal(false);
    };

    const handleProfileClick = () => {
      setShowProfile(true);
      setSelectedBook(null);
    };

    const handleProfileUpdate = (updatedUser) => {
      // 直接更新用户状态，不需要通过登录流程
      setUser(updatedUser);
    };

    const handleTopRatedClick = () => {
      setShowTopRated(true);
      setSelectedBook(null);
      setShowProfile(false);
      setShowAdminPanel(false);
    };

    const handleHomeClick = () => {
      setSelectedBook(null);
      setShowProfile(false);
      setShowTopRated(false);
      setShowAdminPanel(false);
      setShowPublishBook(false);
    };

    const handlePublishBookClick = () => {
      setShowPublishBook(true);
      setSelectedBook(null);
      setShowProfile(false);
      setShowTopRated(false);
      setShowAdminPanel(false);
    };

    const handleAdminPanelClick = () => {
      setShowAdminPanel(true);
      setSelectedBook(null);
      setShowProfile(false);
      setShowTopRated(false);
      setShowPublishBook(false);
    };

    if (authLoading || booksLoading) {
      return <LoadingSpinner />;
    }

    return (
      <div className="min-h-screen bg-gray-50" data-name="app" data-file="src/App.jsx">
        <Header 
          user={user} 
          onLoginClick={handleLoginClick}
          onLogout={logout}
          onProfileClick={handleProfileClick}
          onTopRatedClick={handleTopRatedClick}
          onAdminPanelClick={handleAdminPanelClick}
          onHomeClick={handleHomeClick}
          onPublishBookClick={handlePublishBookClick}
        />
        <main className="container mx-auto px-4 py-6">
          {showProfile ? (
            <UserProfile 
              user={user}
              onBack={() => setShowProfile(false)}
              onProfileUpdate={handleProfileUpdate}
            />
          ) : showTopRated ? (
            <TopRatedBooks 
              books={books}
              onBack={() => setShowTopRated(false)}
              onBookSelect={handleBookSelect}
            />
          ) : showAdminPanel ? (
            <AdminPanel 
              user={user}
              onBack={() => setShowAdminPanel(false)}
            />
          ) : showPublishBook ? (
            <PublishBook 
              user={user}
              onBack={() => setShowPublishBook(false)}
              onBookPublished={() => {
                setShowPublishBook(false);
                // 可以在这里刷新图书列表
              }}
            />
          ) : selectedBook ? (
            <BookDetail 
              book={selectedBook} 
              user={user}
              onBack={() => setSelectedBook(null)}
              onBookStatsUpdate={handleBookStatsUpdate}
            />
          ) : (
            <>
              <SearchBar onSearch={handleSearch} />
              <CategoryNav 
                selectedCategory={selectedCategory} 
                onCategoryChange={handleCategoryChange} 
              />
              <BookGrid 
                books={filteredBooks} 
                onBookSelect={handleBookSelect}
              />
            </>
          )}
        </main>
        
        {showLoginModal && (
          <LoginModal 
            onClose={() => setShowLoginModal(false)}
            onLoginSuccess={handleLoginSuccess}
          />
        )}
      </div>
    );
  } catch (error) {
    console.error('App component error:', error);
    return null;
  }
}

export default function AppWithErrorBoundary() {
  return (
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  );
}
