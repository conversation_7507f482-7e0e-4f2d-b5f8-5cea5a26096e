import React from 'react'

function LoadingSpinner() {
  try {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50" data-name="loading-spinner" data-file="src/components/LoadingSpinner.jsx">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[var(--primary-color)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[var(--text-muted)]">加载中...</p>
        </div>
      </div>
    );
  } catch (error) {
    console.error('LoadingSpinner component error:', error);
    return null;
  }
}

export default LoadingSpinner