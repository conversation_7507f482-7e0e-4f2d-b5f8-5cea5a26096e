import React from 'react'

function BookCard({ book, onClick }) {
  try {
    const renderStars = (rating) => {
      const stars = [];
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating % 1 !== 0;
      
      for (let i = 0; i < fullStars; i++) {
        stars.push(<div key={i} className="icon-star text-sm rating-stars"></div>);
      }
      
      if (hasHalfStar) {
        stars.push(<div key="half" className="icon-star-half text-sm rating-stars"></div>);
      }
      
      const emptyStars = 5 - Math.ceil(rating);
      for (let i = 0; i < emptyStars; i++) {
        stars.push(<div key={`empty-${i}`} className="icon-star text-sm text-gray-300"></div>);
      }
      
      return stars;
    };

    return (
      <div 
        className="book-card cursor-pointer" 
        onClick={onClick}
        data-name="book-card" 
        data-file="src/components/BookCard.jsx"
      >
        <div className="flex space-x-4">
          <div className="flex-shrink-0">
            <img
              src={book.cover}
              alt={book.title}
              className="w-20 h-28 object-cover rounded shadow-sm"
            />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-[var(--text-primary)] mb-1 line-clamp-2">
              {book.title}
            </h3>
            <p className="text-[var(--text-secondary)] mb-2">{book.author}</p>
            
            <div className="flex items-center space-x-2 mb-2">
              <div className="flex items-center space-x-1">
                {renderStars(book.rating)}
              </div>
              <span className="text-sm text-[var(--text-muted)]">
                {book.rating} ({book.reviewCount}人评价)
              </span>
            </div>
            
            <p className="text-sm text-[var(--text-secondary)] line-clamp-2">
              {book.description}
            </p>
            
            <div className="mt-3 flex items-center justify-between">
              <span className="text-sm text-[var(--text-muted)]">
                {book.publisher} · {book.publishYear}
              </span>
              <span className="text-lg font-medium text-[var(--primary-color)]">
                ¥{book.price}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('BookCard component error:', error);
    return null;
  }
}

export default BookCard