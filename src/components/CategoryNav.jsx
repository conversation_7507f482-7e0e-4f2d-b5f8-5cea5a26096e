import React from 'react'

function CategoryNav({ selectedCategory, onCategoryChange }) {
  try {
    const categories = [
      { id: 'all', name: '全部' },
      { id: 'fiction', name: '小说' },
      { id: 'literature', name: '文学' },
      { id: 'history', name: '历史' },
      { id: 'philosophy', name: '哲学' },
      { id: 'psychology', name: '心理学' },
      { id: 'science', name: '科学' },
      { id: 'biography', name: '传记' }
    ];

    return (
      <div className="mb-6" data-name="category-nav" data-file="src/components/CategoryNav.jsx">
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`px-4 py-2 rounded-full text-sm transition-colors ${
                selectedCategory === category.id
                  ? 'bg-[var(--primary-color)] text-white'
                  : 'bg-gray-100 text-[var(--text-secondary)] hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>
    );
  } catch (error) {
    console.error('CategoryNav component error:', error);
    return null;
  }
}

export default CategoryNav