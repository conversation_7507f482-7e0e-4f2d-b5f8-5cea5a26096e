import React, { useState } from 'react'

function SearchBar({ onSearch }) {
  try {
    const [searchTerm, setSearchTerm] = useState('');

    const handleSubmit = (e) => {
      e.preventDefault();
      onSearch(searchTerm);
    };

    const handleChange = (e) => {
      const value = e.target.value;
      setSearchTerm(value);
      onSearch(value);
    };

    return (
      <div className="mb-6" data-name="search-bar" data-file="src/components/SearchBar.jsx">
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={handleChange}
              placeholder="搜索书名、作者..."
              className="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-[var(--border-color)] rounded-lg focus:outline-none focus:border-[var(--primary-color)] focus:ring-1 focus:ring-[var(--primary-color)]"
            />
            <div className="absolute inset-y-0 left-0 flex items-center pl-4">
              <div className="icon-search text-lg text-[var(--text-muted)]"></div>
            </div>
          </div>
        </form>
      </div>
    );
  } catch (error) {
    console.error('SearchBar component error:', error);
    return null;
  }
}

export default SearchBar