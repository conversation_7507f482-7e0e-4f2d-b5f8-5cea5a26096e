import React from 'react'
import BookCard from './BookCard'

function BookGrid({ books, onBookSelect }) {
  try {
    if (books.length === 0) {
      return (
        <div className="text-center py-12" data-name="empty-state" data-file="src/components/BookGrid.jsx">
          <div className="icon-book text-4xl text-gray-300 mb-4"></div>
          <p className="text-[var(--text-muted)]">暂无找到相关图书</p>
        </div>
      );
    }

    return (
      <div className="grid gap-6" data-name="book-grid" data-file="src/components/BookGrid.jsx">
        {books.map(book => (
          <BookCard 
            key={book.id} 
            book={book} 
            onClick={() => onBookSelect(book)}
          />
        ))}
      </div>
    );
  } catch (error) {
    console.error('BookGrid component error:', error);
    return null;
  }
}

export default BookGrid