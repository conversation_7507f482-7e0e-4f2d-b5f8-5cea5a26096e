import React from 'react'

function Header({ user, onLogin<PERSON><PERSON>, onLogout, onProfileClick, onTopRatedClick, onAdminPanelClick, onHomeClick, onPublishBookClick }) {
  try {
    return (
      <header className="bg-white shadow-sm border-b border-[var(--border-color)]" data-name="header" data-file="src/components/Header.jsx">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-[var(--primary-color)] rounded flex items-center justify-center">
                  <div className="icon-book-open text-lg text-white"></div>
                </div>
                <h1 className="text-xl font-bold text-[var(--primary-color)]">花瓣读书</h1>
              </div>
              
              <nav className="hidden md:flex items-center space-x-6">
                <button onClick={onHomeClick} className="nav-link">首页</button>
                <button onClick={onTopRatedClick} className="nav-link">好评榜单</button>
                {user && user.role === 'common' && (
                  <button onClick={onPublishBookClick} className="nav-link">
                    <div className="flex items-center space-x-1">
                      <div className="icon-plus text-lg"></div>
                      <span>发布图书</span>
                    </div>
                  </button>
                )}
                {user && user.role === 'admin' && (
                  <button onClick={onAdminPanelClick} className="nav-link">
                    <div className="flex items-center space-x-1">
                      <div className="icon-settings text-lg"></div>
                      <span>管理</span>
                    </div>
                  </button>
                )}
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-3">
                  <button 
                    onClick={onProfileClick}
                    className="flex items-center space-x-2 hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors"
                  >
                    <img
                      src={user.avatar}
                      alt={user.username}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <span className="text-sm text-[var(--text-primary)] hidden sm:block">
                      {user.username}
                    </span>
                  </button>
                  <button 
                    onClick={onLogout}
                    className="text-sm text-[var(--text-muted)] hover:text-[var(--primary-color)]"
                  >
                    退出
                  </button>
                </div>
              ) : (
                <button 
                  onClick={onLoginClick}
                  className="btn-primary text-sm"
                >
                  登录
                </button>
              )}
            </div>
          </div>
        </div>
      </header>
    );
  } catch (error) {
    console.error('Header component error:', error);
    return null;
  }
}

export default Header