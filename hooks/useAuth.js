// 用户认证状态管理
function useAuth() {
  const [user, setUser] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    try {
      const savedUser = AuthService.getUser();
      if (savedUser) {
        setUser(savedUser);
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const login = React.useCallback(async (emailOrUserData, username) => {
    try {
      setLoading(true);
      let userData;
      
      // 如果第一个参数是对象，说明是直接设置用户数据（用于资料更新）
      if (typeof emailOrUserData === 'object' && emailOrUserData !== null) {
        userData = emailOrUserData;
        setUser(userData);
        return userData;
      }
      
      // 否则是正常的登录流程
      userData = await AuthService.login(emailOrUserData, username);
      setUser(userData);
      return userData;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = React.useCallback(() => {
    try {
      AuthService.logout();
      setUser(null);
    } catch (error) {
      console.error('登出失败:', error);
    }
  }, []);

  return {
    user,
    loading,
    login,
    logout,
    setUser,
    isAuthenticated: !!user
  };
}