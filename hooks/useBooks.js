// 图书数据状态管理
function useBooks() {
  const [books, setBooks] = React.useState([]);
  const [filteredBooks, setFilteredBooks] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [selectedCategory, setSelectedCategory] = React.useState('all');

  // 加载图书数据
  const loadBooks = React.useCallback(async () => {
    try {
      setLoading(true);
      const booksData = await ApiService.getBooks();
      
      // 检查是否有数据
      if (booksData && booksData.length > 0) {
        // 为每本书获取实际的评论统计
        const booksWithStats = await Promise.all(
          booksData.map(async (book) => {
            const stats = await ApiService.getBookReviewStats(book.id);
            return {
              ...book,
              reviewCount: stats.reviewCount,
              rating: stats.reviewCount > 0 ? stats.averageRating : book.rating
            };
          })
        );
        
        setBooks(booksWithStats);
        setFilteredBooks(booksWithStats);
      } else {
        // 如果没有数据或API返回空数组，使用本地数据作为备用
        const fallbackData = getBookData();
        setBooks(fallbackData);
        setFilteredBooks(fallbackData);
      }
    } catch (error) {
      console.error('加载图书失败:', error);
      // 如果API失败，使用本地数据作为备用
      const fallbackData = getBookData();
      setBooks(fallbackData);
      setFilteredBooks(fallbackData);
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    loadBooks();
  }, [loadBooks]);

  // 过滤图书
  const filterBooks = React.useCallback(() => {
    let filtered = books;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(book => book.category === selectedCategory);
    }
    
    if (searchTerm) {
      filtered = filtered.filter(book => 
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    setFilteredBooks(filtered);
  }, [books, selectedCategory, searchTerm]);

  React.useEffect(() => {
    filterBooks();
  }, [filterBooks]);

  const searchBooks = React.useCallback((term) => {
    setSearchTerm(term);
  }, []);

  const filterByCategory = React.useCallback((category) => {
    setSelectedCategory(category);
  }, []);

  const refreshBooks = React.useCallback(() => {
    loadBooks();
  }, [loadBooks]);

  return {
    books,
    filteredBooks,
    loading,
    searchTerm,
    selectedCategory,
    searchBooks,
    filterByCategory,
    refreshBooks
  };
}