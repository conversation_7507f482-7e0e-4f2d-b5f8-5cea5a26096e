// 认证服务 - 管理用户登录状态
const AuthService = {
  // 存储用户信息到localStorage
  setUser(user) {
    try {
      localStorage.setItem('douban_user', JSON.stringify(user));
    } catch (error) {
      console.error('存储用户信息失败:', error);
    }
  },

  // 从localStorage获取用户信息
  getUser() {
    try {
      const userStr = localStorage.getItem('douban_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  },

  // 清除用户信息
  clearUser() {
    try {
      localStorage.removeItem('douban_user');
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  },

  // 简单的邮箱验证
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 模拟登录（实际项目中需要真实的认证）
  async login(email, username) {
    try {
      if (!this.validateEmail(email)) {
        throw new Error('请输入有效的邮箱地址');
      }

      // 检查用户是否已存在
      let user = await ApiService.getUserByEmail(email);
      
      // 如果用户不存在，创建新用户（默认为普通用户）
      if (!user) {
        user = await ApiService.createUser({
          email,
          username: username || email.split('@')[0],
          avatar: `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face`,
          bio: '这个人很懒，什么都没有留下...',
          role: 'common'  // 默认创建普通用户，管理员需要在数据库中手动设置
        });
      }

      this.setUser(user);
      return user;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 登出
  logout() {
    this.clearUser();
  }
};