// API服务层 - 统一管理数据库操作
const ApiService = {
  // 图书相关API
  async getBooks() {
    try {
      const response = await trickleListObjects('book', 100, true);
      return response.items.map(item => ({
        id: item.objectId,
        ...item.objectData,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    } catch (error) {
      console.error('获取图书列表失败:', error);
      // 返回空数组而不是抛出错误，让调用方处理备用数据
      return [];
    }
  },

  async getBookById(bookId) {
    try {
      const book = await trickleGetObject('book', bookId);
      return {
        id: book.objectId,
        ...book.objectData,
        createdAt: book.createdAt,
        updatedAt: book.updatedAt
      };
    } catch (error) {
      console.error('获取图书详情失败:', error);
      throw error;
    }
  },

  // 用户相关API
  async createUser(userData) {
    try {
      const user = await trickleCreateObject('user', {
        ...userData,
        status: 'active',
        role: userData.role || 'common'
      });
      return {
        id: user.objectId,
        ...user.objectData,
        createdAt: user.createdAt
      };
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  },

  // 图书管理API
  async createBook(bookData) {
    try {
      // 移除rating和reviewCount字段，这些应该从review表计算
      const { rating, reviewCount, ...cleanBookData } = bookData;
      const book = await trickleCreateObject('book', cleanBookData);
      return {
        id: book.objectId,
        ...book.objectData,
        createdAt: book.createdAt
      };
    } catch (error) {
      console.error('创建图书失败:', error);
      throw error;
    }
  },

  async updateBook(bookId, bookData) {
    try {
      const book = await trickleUpdateObject('book', bookId, bookData);
      return {
        id: book.objectId,
        ...book.objectData,
        updatedAt: book.updatedAt
      };
    } catch (error) {
      console.error('更新图书失败:', error);
      throw error;
    }
  },

  async deleteBook(bookId) {
    try {
      await trickleDeleteObject('book', bookId);
    } catch (error) {
      console.error('删除图书失败:', error);
      throw error;
    }
  },

  // 评论管理API
  async getAllReviews() {
    try {
      const response = await trickleListObjects('review', 100, true);
      return response.items.map(item => ({
        id: item.objectId,
        ...item.objectData,
        createdAt: item.createdAt
      }));
    } catch (error) {
      console.error('获取所有评论失败:', error);
      return [];
    }
  },

  async deleteReview(reviewId) {
    try {
      await trickleDeleteObject('review', reviewId);
    } catch (error) {
      console.error('删除评论失败:', error);
      throw error;
    }
  },

  async getUserByEmail(email) {
    try {
      const response = await trickleListObjects('user', 100, true);
      const user = response.items.find(item => 
        item.objectData.email === email && item.objectData.status === 'active'
      );
      return user ? {
        id: user.objectId,
        ...user.objectData,
        createdAt: user.createdAt
      } : null;
    } catch (error) {
      console.error('获取用户失败:', error);
      return null;
    }
  },

  async updateUser(userId, userData) {
    try {
      const user = await trickleUpdateObject('user', userId, userData);
      return {
        id: user.objectId,
        ...user.objectData,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  },

  async getUserReviews(userId) {
    try {
      const response = await trickleListObjects('review', 100, true);
      return response.items
        .filter(item => 
          item.objectData.userId === userId && 
          item.objectData.status === 'published'
        )
        .map(item => ({
          id: item.objectId,
          ...item.objectData,
          createdAt: item.createdAt
        }));
    } catch (error) {
      console.error('获取用户评论失败:', error);
      return [];
    }
  },

  // 评论相关API
  async getReviewsByBookId(bookId) {
    try {
      const response = await trickleListObjects('review', 100, true);
      return response.items
        .filter(item => 
          item.objectData.bookId === bookId && 
          item.objectData.status === 'published'
        )
        .map(item => ({
          id: item.objectId,
          ...item.objectData,
          createdAt: item.createdAt
        }));
    } catch (error) {
      console.error('获取评论失败:', error);
      return [];
    }
  },

  async createReview(reviewData) {
    try {
      const review = await trickleCreateObject('review', {
        ...reviewData,
        status: 'published'
      });
      return {
        id: review.objectId,
        ...review.objectData,
        createdAt: review.createdAt
      };
    } catch (error) {
      console.error('创建评论失败:', error);
      throw error;
    }
  },

  // 获取图书评论统计信息
  async getBookReviewStats(bookId) {
    try {
      const response = await trickleListObjects('review', 1000, true);
      const bookReviews = response.items.filter(item => 
        item.objectData.bookId === bookId && 
        item.objectData.status === 'published'
      );
      
      const reviewCount = bookReviews.length;
      const totalRating = bookReviews.reduce((sum, review) => 
        sum + (review.objectData.rating || 0), 0
      );
      const averageRating = reviewCount > 0 ? (totalRating / reviewCount) : 0;
      
      return {
        reviewCount,
        averageRating: Math.round(averageRating * 10) / 10
      };
    } catch (error) {
      console.error('获取评论统计失败:', error);
      return { reviewCount: 0, averageRating: 0 };
    }
  }
};